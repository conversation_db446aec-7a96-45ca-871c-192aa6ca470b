; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino
lib_deps =
	Arduino<PERSON>son@^6.21.3
	bitbank2/zlib_turbo@^1.0.0
monitor_filters = esp32_exception_decoder
build_flags =
	-DCONFIG_ARDUINO_LOOP_STACK_SIZE=16384
	-DCONFIG_ESP_MAIN_TASK_STACK_SIZE=16384
