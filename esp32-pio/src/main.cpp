#include <Arduino.h>
#include <WiFi.h>
#include <HTTPClient.h>
#include <zlib_turbo.h>  // 引入zlib_turbo库

// WiFi 配置
const char* ssid = "tatahome";
const char* password = "m1234567";

// 天气API配置
const char* apiURL = "https://nu33jntuuc.re.qweatherapi.com/v7/weather/now?location=101210101&key=d48d7aaeea044c23b127a8661c74275d";
const char* apiKey = "d48d7aaeea044c23b127a8661c74275d";

// 核心函数声明
bool connectWiFi();
void getWeatherData();

void setup() {
  Serial.begin(115200);
  delay(2000);

  Serial.println("ESP32 天气API测试程序启动...");
  Serial.println("=============================");

  // 核心功能1：连接WiFi
  if (connectWiFi()) {
    Serial.println("WiFi连接成功！");

    // 核心功能2：获取天气数据
    getWeatherData();
  } else {
    Serial.println("WiFi连接失败！");
  }
}

void loop() {
  // 每30秒获取一次天气数据
  delay(8000);

  if (WiFi.status() == WL_CONNECTED) {
    getWeatherData();
  } else {
    Serial.println("WiFi连接断开，尝试重新连接...");
    connectWiFi();
  }
}

// 核心功能1：WiFi连接函数
bool connectWiFi() {
  Serial.println("开始连接WiFi...");
  Serial.print("网络名称: ");
  Serial.println(ssid);

  WiFi.begin(ssid, password);

  int attempts = 0;
  const int maxAttempts = 20;

  while (WiFi.status() != WL_CONNECTED && attempts < maxAttempts) {
    delay(500);
    Serial.print(".");
    attempts++;
  }

  Serial.println();

  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("WiFi连接成功！");
    Serial.print("IP地址: ");
    Serial.println(WiFi.localIP());
    Serial.print("信号强度: ");
    Serial.print(WiFi.RSSI());
    Serial.println(" dBm");
    return true;
  } else {
    Serial.println("WiFi连接失败！");
    return false;
  }
}

// 核心功能2：获取天气数据函数
void getWeatherData() {
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("WiFi未连接，无法获取天气数据");
    return;
  }

  // 打印内存使用情况
  Serial.printf("开始获取天气数据 - 可用内存: %d 字节\n", ESP.getFreeHeap());
  Serial.println();
  Serial.println("正在获取天气数据...");
  Serial.print("API地址: ");
  Serial.println(apiURL);

  HTTPClient http;
  http.begin(apiURL);

  // 设置请求头
  http.addHeader("User-Agent", "ESP32-Weather-Client/1.0");
  http.addHeader("Accept", "application/json");
  http.addHeader("Content-Type", "application/json");
  http.addHeader("Accept-Encoding", "gzip, identity"); // 支持gzip和未压缩

  Serial.println("发送HTTP GET请求...");
  int httpResponseCode = http.GET();

  if (httpResponseCode > 0) {
    Serial.print("HTTP响应代码: ");
    Serial.println(httpResponseCode);

    if (httpResponseCode == 200) {
      Serial.println("HTTP请求成功，开始处理响应数据...");

      // 获取响应流和大小
      WiFiClient *stream = http.getStreamPtr();
      int contentLength = http.getSize();
      String payload = "";

      if (contentLength > 0 && contentLength < 8192) { // 限制最大8KB
        // 分配缓冲区读取原始数据
        uint8_t *buffer = (uint8_t*)malloc(contentLength + 16);
        if (buffer) {
          Serial.printf("分配缓冲区成功，大小: %d 字节\n", contentLength);

          // 读取所有数据
          size_t bytesRead = 0;
          unsigned long startTime = millis();

          while (bytesRead < contentLength && (millis() - startTime) < 10000) {
            if (stream->available()) {
              int bytesToRead = min((int)(contentLength - bytesRead), stream->available());
              int actualRead = stream->readBytes(buffer + bytesRead, bytesToRead);
              bytesRead += actualRead;
              Serial.printf("已读取: %d/%d 字节\n", bytesRead, contentLength);
            } else {
              delay(50);
            }
          }

          if (bytesRead >= 2 && buffer[0] == 0x1f && buffer[1] == 0x8b) {
            // 检测到gzip数据
            Serial.println("检测到gzip压缩数据，开始解压...");

            // 创建静态的zlib_turbo对象以避免栈溢出
            static zlib_turbo zt;
            uint32_t uncompSize = zt.gzip_info(buffer, bytesRead);

            if (uncompSize > 0 && uncompSize < 16384) { // 限制解压后大小16KB
              uint8_t *uncompressed = (uint8_t*)malloc(uncompSize + 16);
              if (uncompressed) {
                int result = zt.gunzip(buffer, bytesRead, uncompressed);
                if (result == ZT_SUCCESS) {
                  uncompressed[uncompSize] = 0; // 确保字符串结束
                  payload = String((char*)uncompressed);
                  Serial.println("gzip解压成功！");
                } else {
                  Serial.printf("gzip解压失败，错误代码: %d\n", result);
                  payload = "解压失败";
                }
                free(uncompressed);
              } else {
                Serial.println("无法分配解压缓冲区");
                payload = "内存不足";
              }
            } else {
              Serial.printf("解压后大小异常: %d\n", uncompSize);
              payload = "数据大小异常";
            }
          } else {
            // 未压缩数据，直接转换
            buffer[bytesRead] = 0; // 确保字符串结束
            payload = String((char*)buffer);
            Serial.println("数据未压缩，直接使用");
          }

          free(buffer);
        } else {
          Serial.println("内存分配失败");
          payload = "内存分配失败";
        }
      } else {
        // 使用getString()作为备选方案
        Serial.printf("内容长度异常(%d)，使用getString()方法\n", contentLength);
        payload = http.getString();
      }

      // 打印最终的JSON数据
      Serial.println();
      Serial.println("========== 天气API返回的JSON数据 ==========");
      Serial.println(payload);
      Serial.println("==========================================");
    }else {
      Serial.print("HTTP请求失败，响应代码: ");
      Serial.println(httpResponseCode);
    }
  } else {
    Serial.print("HTTP请求错误: ");
    Serial.println(http.errorToString(httpResponseCode));
  }

  http.end();
  Serial.println("天气数据获取完成");
  Serial.printf("结束获取天气数据 - 可用内存: %d 字节\n", ESP.getFreeHeap());
  Serial.println();
}

