#include <Arduino.h>
#include <WiFi.h>
#include <HTTPClient.h>
#include <zlib_turbo.h>  // 引入zlib_turbo库

// WiFi 配置
const char* ssid = "tatahome";
const char* password = "m1234567";

// 天气API配置
const char* apiURL = "https://nu33jntuuc.re.qweatherapi.com/v7/weather/now?location=101210101&key=d48d7aaeea044c23b127a8661c74275d";
const char* apiKey = "d48d7aaeea044c23b127a8661c74275d";

// 核心函数声明
bool connectWiFi();
void getWeatherData();

void setup() {
  Serial.begin(115200);
  delay(2000);

  Serial.println("ESP32 天气API测试程序启动...");
  Serial.println("=============================");

  // 核心功能1：连接WiFi
  if (connectWiFi()) {
    Serial.println("WiFi连接成功！");

    // 核心功能2：获取天气数据
    getWeatherData();
  } else {
    Serial.println("WiFi连接失败！");
  }
}

void loop() {
  // 每30秒获取一次天气数据
  delay(8000);

  if (WiFi.status() == WL_CONNECTED) {
    getWeatherData();
  } else {
    Serial.println("WiFi连接断开，尝试重新连接...");
    connectWiFi();
  }
}

// 核心功能1：WiFi连接函数
bool connectWiFi() {
  Serial.println("开始连接WiFi...");
  Serial.print("网络名称: ");
  Serial.println(ssid);

  WiFi.begin(ssid, password);

  int attempts = 0;
  const int maxAttempts = 20;

  while (WiFi.status() != WL_CONNECTED && attempts < maxAttempts) {
    delay(500);
    Serial.print(".");
    attempts++;
  }

  Serial.println();

  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("WiFi连接成功！");
    Serial.print("IP地址: ");
    Serial.println(WiFi.localIP());
    Serial.print("信号强度: ");
    Serial.print(WiFi.RSSI());
    Serial.println(" dBm");
    return true;
  } else {
    Serial.println("WiFi连接失败！");
    return false;
  }
}

// 核心功能2：获取天气数据函数
void getWeatherData() {
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("WiFi未连接，无法获取天气数据");
    return;
  }

  Serial.println();
  Serial.println("正在获取天气数据...");
  Serial.print("API地址: ");
  Serial.println(apiURL);

  HTTPClient http;
  http.begin(apiURL);

  // 设置请求头
  http.addHeader("User-Agent", "ESP32-Weather-Client/1.0");
  http.addHeader("Accept", "application/json");
  http.addHeader("Content-Type", "application/json");
  http.addHeader("Accept-Encoding", "identity"); 

  Serial.println("发送HTTP GET请求...");
  int httpResponseCode = http.GET();

  if (httpResponseCode > 0) {
    Serial.print("HTTP响应代码: ");
    Serial.println(httpResponseCode);

    if (httpResponseCode == 200) {
      // 获取响应数据
      String payload = http.getString();

      // 检查是否是gzip压缩数据
      WiFiClient *stream = http.getStreamPtr();
      size_t len = http.getSize();

      if (len > 0) {
        // 分配缓冲区（额外8字节用于安全读取）
        uint8_t *buffer = (uint8_t*)malloc(len + 8);
        if (buffer) {
          // 重新获取数据流
          http.end();
          http.begin(apiURL);
          http.addHeader("User-Agent", "ESP32-Weather-Client/1.0");
          http.addHeader("Accept", "application/json");
          http.addHeader("Content-Type", "application/json");
          http.addHeader("Accept-Encoding", "gzip"); // 明确请求gzip压缩

          int newResponseCode = http.GET();
          if (newResponseCode == 200) {
            WiFiClient *newStream = http.getStreamPtr();
            size_t newLen = http.getSize();

            // 读取压缩数据
            size_t bytesRead = 0;
            unsigned long startTime = millis();
            while (bytesRead < newLen && (millis() - startTime) < 5000) {
              if (newStream->available()) {
                buffer[bytesRead++] = newStream->read();
              } else {
                delay(10);
              }
            }

            // 检查是否是gzip格式 (magic number: 0x1f, 0x8b)
            if (bytesRead >= 2 && buffer[0] == 0x1f && buffer[1] == 0x8b) {
              Serial.println("检测到gzip压缩数据，开始解压...");

              // 使用zlib_turbo解压
              zlib_turbo zt;
              uint32_t uncompSize = zt.gzip_info(buffer, bytesRead);

              if (uncompSize > 0) {
                uint8_t *uncompressed = (uint8_t*)malloc(uncompSize + 8);
                if (uncompressed) {
                  int result = zt.gunzip(buffer, bytesRead, uncompressed);
                  if (result == ZT_SUCCESS) {
                    // 转换为字符串
                    uncompressed[uncompSize] = 0; // 确保字符串结束
                    payload = String((char*)uncompressed);
                    Serial.println("gzip解压成功！");
                  } else {
                    Serial.printf("gzip解压失败，错误代码: %d\n", result);
                    payload = "解压失败";
                  }
                  free(uncompressed);
                } else {
                  Serial.println("无法分配解压缓冲区");
                  payload = "内存不足";
                }
              } else {
                Serial.println("无法获取解压后大小");
                payload = "gzip信息错误";
              }
            } else {
              // 不是gzip数据，直接使用
              payload = String((char*)buffer, bytesRead);
              Serial.println("数据未压缩，直接使用");
            }
          } else {
            payload = "重新请求失败";
          }
          free(buffer);
        } else {
          payload = "内存分配失败";
        }
      } else {
        // 如果没有内容长度，直接使用getString()的结果
        Serial.println("使用直接响应数据");
      }

      // 打印最终的JSON数据
      Serial.println();
      Serial.println("========== 天气API返回的JSON数据 ==========");
      Serial.println(payload);
      Serial.println("==========================================");
    }else {
      Serial.print("HTTP请求失败，响应代码: ");
      Serial.println(httpResponseCode);
    }
  } else {
    Serial.print("HTTP请求错误: ");
    Serial.println(http.errorToString(httpResponseCode));
  }

  http.end();
  Serial.println("天气数据获取完成");
  Serial.println();
}

